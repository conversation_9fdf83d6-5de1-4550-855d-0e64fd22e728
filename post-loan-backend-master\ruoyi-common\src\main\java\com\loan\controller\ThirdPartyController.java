package com.loan.controller;

import com.loan.common.Result;
import com.loan.config.ThirdPartyConfig;
import com.loan.dto.ThirdPartyRequestDto;
import com.loan.service.ThirdPartyService;
import com.loan.util.AESUtil;
import com.loan.util.SignUtil;
import com.ruoyi.common.annotation.Anonymous;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 第三方数据接收接口
 * 根据接口文档规范实现
 * 接口地址: /api/third-party/receive-data
 * 请求方式: POST
 * Content-Type: application/json
 * 需要认证：移除@Anonymous注解，使用系统默认认证机制
 */
@RestController
@RequestMapping("/api/third-party")
@Slf4j
public class ThirdPartyController {

    @Autowired
    private ThirdPartyService thirdPartyService;

    @Autowired
    private ThirdPartyConfig thirdPartyConfig;

    /**
     * 接收第三方加密数据
     * 支持AES加密、签名验证、时间戳验证等安全机制
     * @param requestDto 请求数据
     * @param request HTTP请求
     * @return 处理结果
     */
    @Anonymous
    @PostMapping("/receive-data")
    public Result<String> receiveThirdPartyData(@RequestBody ThirdPartyRequestDto requestDto,
                                               HttpServletRequest request) {
        try {
            log.info("接收第三方数据请求，合作方: {}, 来源IP: {}",
                requestDto != null ? requestDto.getPartnerId() : "unknown",
                getClientIp(request));

            // 1. 验证必要参数
            if (requestDto == null) {
                return Result.badRequest("请求体不能为空");
            }

            if (requestDto.getPartnerId() == null || requestDto.getPartnerId().trim().isEmpty()) {
                return Result.badRequest("请求参数不完整");
            }

            if (requestDto.getData() == null || requestDto.getData().trim().isEmpty()) {
                return Result.badRequest("请求参数不完整");
            }

            if (requestDto.getTimestamp() == null) {
                return Result.badRequest("请求参数不完整");
            }

            if (requestDto.getSign() == null || requestDto.getSign().trim().isEmpty()) {
                return Result.badRequest("请求参数不完整");
            }

            // 2. 验证合作方白名单
            if (!thirdPartyConfig.getPartners().isPartnerAllowed(requestDto.getPartnerId())) {
                log.warn("非法合作方访问: {}, IP: {}", requestDto.getPartnerId(), getClientIp(request));
                return Result.error(403, "非法合作方");
            }

            // 2.1 验证IP白名单（如果启用）
            String clientIp = getClientIp(request);
            if (!thirdPartyConfig.getPartners().isIpAllowed(clientIp)) {
                log.warn("非法IP访问: {}, 合作方: {}", clientIp, requestDto.getPartnerId());
                return Result.error(403, "IP地址不在白名单中");
            }

            // 3. 验证车架号和车辆状态（如果提供）
            if (requestDto.getVin() != null && !requestDto.getVin().isEmpty()) {
                if (requestDto.getVin().length() != 17) {
                    return Result.badRequest("车架号格式不正确，应为17位字符");
                }
            }

            if (requestDto.getStatus() != null && requestDto.getStatus() < 0) {
                return Result.badRequest("车辆状态值不能为负数");
            }
            
            // 2. 验证时间戳（防重放攻击）
            if (!validateTimestamp(requestDto.getTimestamp())) {
                return Result.error("请求时间戳无效");
            }

            // 3. 验证时间戳（防重放攻击，5分钟内有效）
            if (!validateTimestamp(requestDto.getTimestamp())) {
                log.warn("时间戳验证失败，合作方: {}, 时间戳: {}", requestDto.getPartnerId(), requestDto.getTimestamp());
                return Result.timestampInvalid();
            }

            // 4. 验证签名
            if (!validateSign(requestDto)) {
                log.warn("签名验证失败，合作方: {}, 签名: {}", requestDto.getPartnerId(), requestDto.getSign());
                return Result.signatureError();
            }

            // 5. AES解密数据
            String decryptedData = AESUtil.decrypt(requestDto.getData());
            if (decryptedData == null) {
                log.error("AES解密失败，合作方: {}", requestDto.getPartnerId());
                return Result.error("数据解密失败");
            }

            log.info("数据解密成功，合作方: {}", requestDto.getPartnerId());

            // 6. 处理业务逻辑并存储数据
            String result = thirdPartyService.processThirdPartyData(decryptedData, requestDto, request);

            log.info("第三方数据处理成功，合作方: {}", requestDto.getPartnerId());
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("处理第三方数据异常", e);
            return Result.error("系统异常");
        }
    }

    /**
     * 验证时间戳（根据配置的有效期）
     */
    private boolean validateTimestamp(Long timestamp) {
        if (timestamp == null) {
            return false;
        }

        long currentTime = System.currentTimeMillis();
        long timeDiff = Math.abs(currentTime - timestamp);

        // 根据配置的有效期验证（分钟转毫秒）
        long validityMillis = thirdPartyConfig.getPartners().getTimestampValidity() * 60 * 1000;
        return timeDiff <= validityMillis;
    }
    
    /**
     * 验证签名
     */
    private boolean validateSign(ThirdPartyRequestDto requestDto) {
        try {
            String expectedSign = SignUtil.generateSign(requestDto);
            boolean isValid = expectedSign.equals(requestDto.getSign());

            if (!isValid) {
                log.warn("签名验证失败 - 合作方: {}, 期望签名: {}, 实际签名: {}",
                    requestDto.getPartnerId(), expectedSign, requestDto.getSign());
            } else {
                log.debug("签名验证成功 - 合作方: {}", requestDto.getPartnerId());
            }

            return isValid;
        } catch (Exception e) {
            log.error("签名验证异常 - 合作方: {}", requestDto.getPartnerId(), e);
            return false;
        }
    }
    



    /**
     * 获取客户端IP
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}
